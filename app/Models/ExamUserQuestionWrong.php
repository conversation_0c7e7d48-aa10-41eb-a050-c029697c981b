<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ExamUserQuestionWrong extends Model
{
    use HasFactory;

    /**
     * 指定表名（配置文件已设置yjp_前缀）
     */
    protected $table = 'exam_user_question_wrong';

    /**
     * 类型常量
     */
    const TYPE_SYSTEM_ANATOMY = 1;  // 系统解剖学
    const TYPE_LOCAL_ANATOMY = 2;   // 局部解剖学

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'question_id',
        'user_id',
        'type',
        'category_parent_id',
        'category_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 关联试题
     */
    public function question()
    {
        return $this->belongsTo(ExamQuestionPublic::class, 'question_id', 'id');
    }

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    /**
     * 关联分类
     */
    public function category()
    {
        return $this->belongsTo(Category::class, 'category_id', 'id');
    }

    /**
     * 关联父分类
     */
    public function parentCategory()
    {
        return $this->belongsTo(Category::class, 'category_parent_id', 'id');
    }

    /**
     * 获取类型名称
     */
    public function getTypeNameAttribute()
    {
        return self::getTypeOptions()[$this->type] ?? '未知';
    }

    /**
     * 获取类型选项
     */
    public static function getTypeOptions()
    {
        return [
            self::TYPE_SYSTEM_ANATOMY => '系统解剖学',
            self::TYPE_LOCAL_ANATOMY => '局部解剖学',
        ];
    }
}
