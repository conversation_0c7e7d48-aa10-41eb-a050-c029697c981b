<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ExamRecordQuestion extends Model
{
    use HasFactory;

    /**
     * 指定表名（配置文件已设置yjp_前缀）
     */
    protected $table = 'exam_record_question';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'record_id',
        'question_id',
        'question_sn',
        'question_type',
        'question_score',
        'user_answer',
        'correct_answer',
        'is_correct',
        'score',
        'more',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'user_answer' => 'array',
        'correct_answer' => 'array',
        'more' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 关联到考试记录
     */
    public function examRecord()
    {
        return $this->belongsTo(ExamRecord::class, 'record_id');
    }

    /**
     * 关联到试题
     */
    public function question()
    {
        return $this->belongsTo(ExamQuestionPublic::class, 'question_id');
    }

    /**
     * 检查答案是否正确
     */
    public function isAnswerCorrect()
    {
        return $this->is_correct == 1;
    }

    /**
     * 获取得分率
     */
    public function getScoreRateAttribute()
    {
        if ($this->question_score <= 0) {
            return 0;
        }
        return round(($this->score / $this->question_score) * 100, 2);
    }

    /**
     * 格式化用户答案显示
     */
    public function getFormattedUserAnswerAttribute()
    {
        if (is_array($this->user_answer)) {
            return implode(', ', $this->user_answer);
        }
        return $this->user_answer ?? '';
    }

    /**
     * 格式化正确答案显示
     */
    public function getFormattedCorrectAnswerAttribute()
    {
        if (is_array($this->correct_answer)) {
            return implode(', ', $this->correct_answer);
        }
        return $this->correct_answer ?? '';
    }

    /**
     * 判断答案是否正确并计分
     *
     * @param array|string $userAnswer 用户答案
     * @param array|string $correctAnswer 正确答案
     * @param int $questionType 题型
     * @param float $questionScore 题目分数
     * @return array
     */
    public static function checkAnswer($userAnswer, $correctAnswer, int $questionType, float $questionScore): array
    {
        // 标准化答案格式
        $userAnswer = is_array($userAnswer) ? $userAnswer : [$userAnswer];
        $correctAnswer = is_array($correctAnswer) ? $correctAnswer : [$correctAnswer];

        // 移除空值并排序
        $userAnswer = array_filter(array_map('trim', $userAnswer));
        $correctAnswer = array_filter(array_map('trim', $correctAnswer));
        sort($userAnswer);
        sort($correctAnswer);

        $isCorrect = false;
        $score = 0;

        switch ($questionType) {
            case ExamQuestionPublic::TYPE_SINGLE:   // 单选题
            case ExamQuestionPublic::TYPE_JUDGE:    // 判断题
                $isCorrect = count($userAnswer) === 1 && count($correctAnswer) === 1 && 
                           strtolower($userAnswer[0]) === strtolower($correctAnswer[0]);
                $score = $isCorrect ? $questionScore : 0;
                break;

            case ExamQuestionPublic::TYPE_MULTIPLE: // 多选题
                $isCorrect = $userAnswer === $correctAnswer;
                $score = $isCorrect ? $questionScore : 0;
                break;

            case ExamQuestionPublic::TYPE_FILL:     // 填空题
                // 填空题支持部分得分
                $correctCount = 0;
                $totalBlanks = count($correctAnswer);
                
                for ($i = 0; $i < $totalBlanks; $i++) {
                    if (isset($userAnswer[$i]) && 
                        strtolower(trim($userAnswer[$i])) === strtolower(trim($correctAnswer[$i]))) {
                        $correctCount++;
                    }
                }
                
                $isCorrect = $correctCount === $totalBlanks;
                $score = $totalBlanks > 0 ? ($correctCount / $totalBlanks) * $questionScore : 0;
                break;

            case ExamQuestionPublic::TYPE_ESSAY:    // 问答题
                // 问答题需要人工批阅，暂时给0分
                $isCorrect = false;
                $score = 0;
                break;

            default:
                $isCorrect = false;
                $score = 0;
                break;
        }

        return [
            'is_correct' => $isCorrect ? 1 : 0,
            'score' => round($score, 2),
        ];
    }
}
