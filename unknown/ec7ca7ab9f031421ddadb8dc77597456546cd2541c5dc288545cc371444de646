<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

class ExamPaper extends BaseModel
{
    use HasFactory, SoftDeletes;

    /**
     * 指定表名
     */
    protected $table = 'exam_paper';

    /**
     * 创建类型常量
     */
    const CREATE_TYPE_EXAM = 1;      // 考试
    const CREATE_TYPE_HOMEWORK = 2;  // 作业

    /**
     * 试卷类型常量
     */
    const PAPER_TYPE_STANDARD = 1;   // 标准试卷
    const PAPER_TYPE_TEXT = 2;       // 文字题试卷
    const PAPER_TYPE_IMAGE = 3;      // 图片题试卷
    const PAPER_TYPE_SPECIMEN = 4;   // 3D实物标本题试卷

    /**
     * 一键批阅常量
     */
    const ONE_KEY_READ_SUPPORT = 1;     // 支持
    const ONE_KEY_READ_NOT_SUPPORT = 2; // 不支持

    /**
     * 试卷状态常量
     */
    const STATUS_DRAFT = 0;      // 草稿状态
    const STATUS_COMPLETED = 1;  // 完成状态
    const STATUS_PUBLISHED = 2;  // 已发布状态

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'create_type',
        'paper_type',
        'one_key_read',
        'title',
        'questions',
        'more',
        'status',
        'last_edit_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'questions' => 'array',
        'more' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
        'last_edit_at' => 'datetime',
    ];

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    /**
     * 获取创建类型名称
     */
    public function getCreateTypeNameAttribute()
    {
        return self::getCreateTypeOptions()[$this->create_type] ?? '未知';
    }

    /**
     * 获取试卷类型名称
     */
    public function getPaperTypeNameAttribute()
    {
        return self::getPaperTypeOptions()[$this->paper_type] ?? '未知';
    }

    /**
     * 获取一键批阅状态名称
     */
    public function getOneKeyReadNameAttribute()
    {
        return $this->one_key_read === self::ONE_KEY_READ_SUPPORT ? '支持' : '不支持';
    }

    /**
     * 获取试卷状态名称
     */
    public function getStatusNameAttribute()
    {
        return self::getStatusOptions()[$this->status] ?? '未知';
    }

    /**
     * 检查是否为草稿状态
     */
    public function isDraft()
    {
        return $this->status === self::STATUS_DRAFT;
    }

    /**
     * 检查是否已完成
     */
    public function isCompleted()
    {
        return $this->status === self::STATUS_COMPLETED;
    }

    /**
     * 检查是否已发布
     */
    public function isPublished()
    {
        return $this->status === self::STATUS_PUBLISHED;
    }

    /**
     * 检查是否可以编辑
     */
    public function canEdit()
    {
        return $this->status === self::STATUS_DRAFT;
    }

    /**
     * 获取试题总数
     */
    public function getQuestionCountAttribute()
    {
        return count($this->questions ?? []);
    }

    /**
     * 获取总分
     */
    public function getTotalScoreAttribute()
    {
        $totalScore = 0;
        foreach ($this->questions ?? [] as $question) {
            $totalScore += $question['score'] ?? 0;
        }
        return $totalScore;
    }

    /**
     * 获取创建类型选项
     */
    public static function getCreateTypeOptions()
    {
        return [
            self::CREATE_TYPE_EXAM => '考试',
            self::CREATE_TYPE_HOMEWORK => '作业',
        ];
    }

    /**
     * 获取试卷类型选项
     */
    public static function getPaperTypeOptions()
    {
        return [
            self::PAPER_TYPE_STANDARD => '标准试卷',
            self::PAPER_TYPE_TEXT => '文字题试卷',
            self::PAPER_TYPE_IMAGE => '图片题试卷',
            self::PAPER_TYPE_SPECIMEN => '3D实物标本题试卷',
        ];
    }

    /**
     * 获取状态选项
     */
    public static function getStatusOptions()
    {
        return [
            self::STATUS_DRAFT => '草稿',
            self::STATUS_COMPLETED => '已完成',
            self::STATUS_PUBLISHED => '已发布',
        ];
    }
}
