<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ExamRecord extends Model
{
    use HasFactory;

    /**
     * 指定表名（配置文件已设置yjp_前缀）
     */
    protected $table = 'exam_record';

    /**
     * 状态常量
     */
    const STATUS_IN_PROGRESS = 20;  // 考试中
    const STATUS_COMPLETED = 30;    // 已完成
    const STATUS_TIMEOUT = 40;      // 超时

    /**
     * 批阅状态常量
     */
    const READ_OVER_COMPLETED = 1;   // 已批阅
    const READ_OVER_INCOMPLETE = 2;  // 未批阅

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'exam_id',
        'used_seconds',
        'score',
        'status',
        'read_over_status',
        'begin_paper_at',
        'end_paper_at',
        'pass_score',
        'more',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'more' => 'array',
        'begin_paper_at' => 'datetime',
        'end_paper_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 关联到用户
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 关联到考试/作业
     */
    public function exam()
    {
        return $this->belongsTo(Exam::class);
    }

    /**
     * 关联到答题记录
     */
    public function questionRecords()
    {
        return $this->hasMany(ExamRecordQuestion::class, 'record_id');
    }

    /**
     * 获取状态名称
     */
    public function getStatusNameAttribute()
    {
        return self::getStatusOptions()[$this->status] ?? '未知';
    }

    /**
     * 获取批阅状态名称
     */
    public function getReadOverStatusNameAttribute()
    {
        return $this->read_over_status === self::READ_OVER_COMPLETED ? '已批阅' : '未批阅';
    }

    /**
     * 检查是否进行中
     */
    public function isInProgress()
    {
        return $this->status === self::STATUS_IN_PROGRESS;
    }

    /**
     * 检查是否已完成
     */
    public function isCompleted()
    {
        return $this->status === self::STATUS_COMPLETED;
    }

    /**
     * 检查是否超时
     */
    public function isTimeout()
    {
        return $this->status === self::STATUS_TIMEOUT;
    }

    /**
     * 检查是否及格
     */
    public function isPassed()
    {
        return $this->score >= $this->pass_score;
    }

    /**
     * 获取用时（格式化）
     */
    public function getFormattedUsedTimeAttribute()
    {
        $hours = floor($this->used_seconds / 3600);
        $minutes = floor(($this->used_seconds % 3600) / 60);
        $seconds = $this->used_seconds % 60;

        if ($hours > 0) {
            return sprintf('%d小时%d分钟%d秒', $hours, $minutes, $seconds);
        } elseif ($minutes > 0) {
            return sprintf('%d分钟%d秒', $minutes, $seconds);
        } else {
            return sprintf('%d秒', $seconds);
        }
    }

    /**
     * 获取状态选项
     */
    public static function getStatusOptions()
    {
        return [
            self::STATUS_IN_PROGRESS => '进行中',
            self::STATUS_COMPLETED => '已完成',
            self::STATUS_TIMEOUT => '超时',
        ];
    }

    /**
     * 获取批阅状态选项
     */
    public static function getReadOverStatusOptions()
    {
        return [
            self::READ_OVER_COMPLETED => '已批阅',
            self::READ_OVER_INCOMPLETE => '未批阅',
        ];
    }
}
