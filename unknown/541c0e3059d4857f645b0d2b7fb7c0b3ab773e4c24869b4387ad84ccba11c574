<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StudentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true; // 在中间件中已经处理了认证
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = [];
        
        switch ($this->method()) {
            case 'POST':
                if ($this->routeIs('admin.students.store')) {
                    // 创建学生
                    $rules = [
                        'name' => 'required|string|max:255|unique:users,name',
                        'password' => 'required|string|min:6',
                        'number' => 'required|string|max:255|unique:users,number',
                    ];
                } elseif ($this->routeIs('admin.students.update')) {
                    // 更新学生
                    $studentId = $this->input('id');
                    $rules = [
                        'id' => 'required|integer|exists:users,id',
                        'name' => [
                            'required',
                            'string',
                            'max:255',
                            Rule::unique('users', 'name')->ignore($studentId)
                        ],
                        'number' => [
                            'required',
                            'string',
                            'max:255',
                            Rule::unique('users', 'number')->ignore($studentId)
                        ],
                        'password' => 'nullable|string|min:6',
                    ];
                } elseif ($this->routeIs('admin.students.show') || $this->routeIs('admin.students.destroy')) {
                    // 查看或删除学生
                    $rules = [
                        'id' => 'required|integer|exists:users,id',
                    ];
                } elseif ($this->routeIs('admin.students.batch-destroy')) {
                    // 批量删除
                    $rules = [
                        'ids' => 'required|array|min:1',
                        'ids.*' => 'integer|exists:users,id',
                    ];
                } elseif ($this->routeIs('admin.students.import')) {
                    // 导入学生
                    $rules = [
                        'file' => 'required|file|mimes:xlsx,xls|max:10240', // 最大10MB
                    ];
                }
                break;
                
            case 'GET':
                if ($this->routeIs('admin.students.index')) {
                    // 获取学生列表
                    $rules = [
                        'per_page' => 'nullable|integer|min:1|max:100',
                        'search' => 'nullable|string|max:255',
                        'sort_by' => 'nullable|string|in:id,name,number,created_at,login_time',
                        'sort_order' => 'nullable|string|in:asc,desc',
                    ];
                } elseif ($this->routeIs('admin.students.export')) {
                    // 导出学生
                    $rules = [
                        'search' => 'nullable|string|max:255',
                    ];
                }
                break;
        }

        return $rules;
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array
     */
    public function messages()
    {
        return [
            // 通用验证消息
            'id.required' => '学生ID不能为空',
            'id.integer' => '学生ID必须是整数',
            'id.exists' => '学生不存在',
            
            // 姓名验证消息
            'name.required' => '姓名不能为空',
            'name.string' => '姓名必须是字符串',
            'name.max' => '姓名不能超过255个字符',
            'name.unique' => '姓名已存在',
            
            // 密码验证消息
            'password.required' => '密码不能为空',
            'password.string' => '密码必须是字符串',
            'password.min' => '密码至少6位',
            
            // 学号验证消息
            'number.required' => '学号不能为空',
            'number.string' => '学号必须是字符串',
            'number.max' => '学号不能超过255个字符',
            'number.unique' => '学号已存在',
            
            // 批量操作验证消息
            'ids.required' => '学生ID列表不能为空',
            'ids.array' => '学生ID列表必须是数组',
            'ids.min' => '至少选择一个学生',
            'ids.*.integer' => '学生ID必须是整数',
            'ids.*.exists' => '存在无效的学生ID',
            
            // 文件上传验证消息
            'file.required' => '请选择要导入的Excel文件',
            'file.file' => '上传的文件无效',
            'file.mimes' => '文件格式必须是Excel（.xlsx或.xls）',
            'file.max' => '文件大小不能超过10MB',
            
            // 列表查询验证消息
            'per_page.integer' => '每页数量必须是整数',
            'per_page.min' => '每页数量至少为1',
            'per_page.max' => '每页数量不能超过100',
            'search.string' => '搜索关键词必须是字符串',
            'search.max' => '搜索关键词不能超过255个字符',
            'sort_by.string' => '排序字段必须是字符串',
            'sort_by.in' => '排序字段无效',
            'sort_order.string' => '排序方向必须是字符串',
            'sort_order.in' => '排序方向必须是asc或desc',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'id' => '学生ID',
            'name' => '姓名',
            'password' => '密码',
            'number' => '学号',
            'ids' => '学生ID列表',
            'file' => 'Excel文件',
            'per_page' => '每页数量',
            'search' => '搜索关键词',
            'sort_by' => '排序字段',
            'sort_order' => '排序方向',
        ];
    }
}
