<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

class ExamQuestionPublic extends BaseModel
{
    use HasFactory, SoftDeletes;

    /**
     * 指定表名
     */
    protected $table = 'exam_question_public';

    /**
     * 状态常量
     */
    const STATUS_SHOW = 1;   // 显示
    const STATUS_HIDE = 2;   // 隐藏

    /**
     * 题型常量
     */
    const TYPE_SINGLE = 1;      // 单选
    const TYPE_MULTIPLE = 2;    // 多选
    const TYPE_FILL = 3;        // 填空
    const TYPE_JUDGE = 4;       // 判断
    const TYPE_ESSAY = 5;       // 问答
    const TYPE_SPECIMEN = 8;    // 标本指认题
    const TYPE_MODEL = 9;       // 模型拼装题

    /**
     * 试题类型常量
     */
    const QUESTION_TYPE_TEXT = 1;      // 文字题
    const QUESTION_TYPE_IMAGE = 2;     // 图片题
    const QUESTION_TYPE_SPECIMEN = 3;  // 标本题

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'question_sn',
        'category_id',
        'title',
        'type',
        'more',
        'tags',
        'status',
        'question_type',
        'post_title',
        'post_sn',
        'model_name',
        'answer_content',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'more' => 'array',
        'tags' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * 关联分类
     */
    public function category()
    {
        return $this->belongsTo(Category::class, 'category_id', 'id');
    }

    /**
     * 关联分类（通过关联表）
     */
    public function categories()
    {
        return $this->belongsToMany(Category::class, 'category_question', 'question_id', 'category_id');
    }

    /**
     * 获取题型名称
     */
    public function getTypeNameAttribute()
    {
        return self::getTypeOptions()[$this->type] ?? '未知';
    }

    /**
     * 获取试题类型名称
     */
    public function getQuestionTypeNameAttribute()
    {
        return self::getQuestionTypeOptions()[$this->question_type] ?? '未知';
    }

    /**
     * 获取状态名称
     */
    public function getStatusNameAttribute()
    {
        return $this->status === self::STATUS_SHOW ? '显示' : '隐藏';
    }

    /**
     * 获取答案
     */
    public function getAnswerAttribute()
    {
        return $this->more['answer'] ?? [];
    }

    /**
     * 获取选项
     */
    public function getOptionsAttribute()
    {
        return $this->more['items'] ?? [];
    }

    /**
     * 获取答案解析
     */
    public function getAnswerParseAttribute()
    {
        return $this->more['answer_parse'] ?? '';
    }

    /**
     * 检查是否显示
     */
    public function isVisible()
    {
        return $this->status === self::STATUS_SHOW;
    }

    /**
     * 获取题型选项
     */
    public static function getTypeOptions()
    {
        return [
            self::TYPE_SINGLE => '单选题',
            self::TYPE_MULTIPLE => '多选题',
            self::TYPE_FILL => '填空题',
            self::TYPE_JUDGE => '判断题',
            self::TYPE_ESSAY => '问答题',
            self::TYPE_SPECIMEN => '标本指认题',
            self::TYPE_MODEL => '模型拼装题',
        ];
    }

    /**
     * 获取试题类型选项
     */
    public static function getQuestionTypeOptions()
    {
        return [
            self::QUESTION_TYPE_TEXT => '文字题',
            self::QUESTION_TYPE_IMAGE => '图片题',
            self::QUESTION_TYPE_SPECIMEN => '标本题',
        ];
    }

    /**
     * 获取状态选项
     */
    public static function getStatusOptions()
    {
        return [
            self::STATUS_SHOW => '显示',
            self::STATUS_HIDE => '隐藏',
        ];
    }
}
