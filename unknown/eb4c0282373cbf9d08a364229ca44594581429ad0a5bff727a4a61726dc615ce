<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class FolderCourseware extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * 指定表名（配置文件已设置yjp_前缀）
     */
    protected $table = 'folder_courseware';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'folder_id',
        'courseware_id',
        'user_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * 关联到文件夹
     */
    public function folder()
    {
        return $this->belongsTo(Folder::class);
    }

    /**
     * 关联到课件
     */
    public function courseware()
    {
        return $this->belongsTo(Courseware::class);
    }

    /**
     * 关联到用户（教师）
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
