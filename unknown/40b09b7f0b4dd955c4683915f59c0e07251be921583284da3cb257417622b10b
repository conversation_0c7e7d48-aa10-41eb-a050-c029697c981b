<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;

class Folder extends BaseModel
{
    use HasFactory;

    /**
     * 指定表名（配置文件已设置yjp_前缀）
     */
    protected $table = 'folder';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'parent_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * 获取父文件夹
     */
    public function parent()
    {
        return $this->belongsTo(Folder::class, 'parent_id');
    }

    /**
     * 获取子文件夹
     */
    public function children()
    {
        return $this->hasMany(Folder::class, 'parent_id');
    }

    /**
     * 获取所有子孙文件夹（递归）
     */
    public function descendants()
    {
        return $this->children()->with('descendants');
    }

    /**
     * 获取所有祖先文件夹（面包屑导航）
     */
    public function ancestors()
    {
        $ancestors = collect();
        $current = $this->parent;

        while ($current) {
            $ancestors->prepend($current);
            $current = $current->parent;
        }

        return $ancestors;
    }

    /**
     * 获取文件夹路径（面包屑）
     */
    public function getPathAttribute()
    {
        $path = $this->ancestors()->pluck('name')->toArray();
        $path[] = $this->name;
        return implode(' / ', $path);
    }

    /**
     * 获取文件夹层级深度
     */
    public function getDepthAttribute()
    {
        return $this->ancestors()->count();
    }

    /**
     * 检查是否为根文件夹
     */
    public function isRoot()
    {
        return is_null($this->parent_id);
    }

    /**
     * 检查是否为叶子节点（没有子文件夹）
     */
    public function isLeaf()
    {
        return $this->children()->count() === 0;
    }

    /**
     * 检查是否为指定文件夹的子孙
     */
    public function isDescendantOf(Folder $folder)
    {
        $current = $this->parent;

        while ($current) {
            if ($current->id === $folder->id) {
                return true;
            }
            $current = $current->parent;
        }

        return false;
    }

    /**
     * 获取根文件夹
     */
    public static function roots()
    {
        return static::whereNull('parent_id');
    }

    /**
     * 关联到课件（通过中间表）
     */
    public function courseware()
    {
        return $this->belongsToMany(Courseware::class, 'folder_courseware', 'folder_id', 'courseware_id')
                    ->withPivot('user_id')
                    ->withTimestamps();
    }

    /**
     * 获取文件夹中的课件数量
     */
    public function getCoursewareCountAttribute()
    {
        return $this->courseware()->count();
    }

    /**
     * 检查文件夹是否真的为空（没有子文件夹和课件）
     */
    public function isTrulyEmpty()
    {
        return $this->isLeaf() && $this->courseware_count === 0;
    }

    /**
     * 获取文件夹及其所有子文件夹中的课件
     */
    public function getAllCourseware()
    {
        $courseware = collect();

        // 获取当前文件夹的课件
        $courseware = $courseware->merge($this->courseware);

        // 递归获取子文件夹的课件
        foreach ($this->children as $child) {
            $courseware = $courseware->merge($child->getAllCourseware());
        }

        return $courseware;
    }

    /**
     * 构建树形结构
     */
    public static function buildTree($parentId = null, $includeCourseware = false)
    {
        $query = static::where('parent_id', $parentId)
            ->with(['children' => function ($query) {
                $query->orderBy('name');
            }]);

        if ($includeCourseware) {
            $query->with(['courseware' => function ($query) {
                $query->orderBy('created_at', 'desc');
            }]);
        }

        return $query->orderBy('name')
            ->get()
            ->map(function ($folder) use ($includeCourseware) {
                $data = [
                    'id' => $folder->id,
                    'name' => $folder->name,
                    'parent_id' => $folder->parent_id,
                    'depth' => $folder->depth,
                    'is_leaf' => $folder->isLeaf(),
                    'children_count' => $folder->children()->count(),
                    'courseware_count' => $folder->courseware()->count(),
                    'created_at' => $folder->created_at->format('Y-m-d H:i:s'),
                    'children' => $folder->children->isNotEmpty() ?
                        static::buildTree($folder->id, $includeCourseware) : []
                ];

                if ($includeCourseware && $folder->courseware) {
                    $data['courseware'] = $folder->courseware->map(function ($courseware) {
                        return [
                            'id' => $courseware->id,
                            'name' => $courseware->name,
                            'type' => $courseware->type,
                            'type_name' => $courseware->type_name,
                            'file_size' => $courseware->file_size,
                            'file_extension' => $courseware->file_extension,
                            'created_at' => $courseware->created_at->format('Y-m-d H:i:s'),
                        ];
                    });
                }

                return $data;
            });
    }
}
