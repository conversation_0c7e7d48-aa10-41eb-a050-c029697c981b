<?php

namespace App\Http\Controllers\Api\Student;

use App\Http\Controllers\Controller;
use App\Models\Exam;
use App\Models\ExamPaper;
use App\Models\ExamRecord;
use App\Models\ExamRecordQuestion;
use App\Models\ExamQuestionPublic;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Tymon\JWTAuth\Facades\JWTAuth;

class AssignmentController extends Controller
{
    /**
     * 获取作业列表
     *
     * @param Request $request
     * @return array
     */
    public function index(Request $request): array
    {
        try {
            // 获取当前用户
            $currentUser = JWTAuth::parseToken()->authenticate();
            if (!$currentUser) {
                return [
                    'code' => 401,
                    'message' => '用户未登录',
                    'data' => []
                ];
            }

            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'page' => 'nullable|integer|min:1',
                'page_size' => 'nullable|integer|min:1|max:50',
            ]);

            if ($validator->fails()) {
                return [
                    'code' => 400,
                    'message' => $validator->errors()->first(),
                    'data' => []
                ];
            }

            $page = max(1, (int) $request->input('page', 1));
            $pageSize = min(50, max(1, (int) $request->input('page_size', 20)));

            // 获取学生所在的班级ID列表
            $classIds = $currentUser->examClasses()->pluck('exam_class.id')->toArray();

            if (empty($classIds)) {
                return [
                    'code' => 200,
                    'message' => '获取作业列表成功',
                    'data' => [
                        'assignments' => [],
                        'statistics' => [
                            'total' => 0,
                            'completed' => 0,
                            'pending' => 0,
                        ],
                        'pagination' => [
                            'current_page' => $page,
                            'page_size' => $pageSize,
                            'total' => 0,
                            'total_pages' => 0,
                            'has_more' => false,
                        ]
                    ]
                ];
            }

            // 构建查询 - 只查询作业类型的考试
            $query = Exam::query()
                ->with(['examClass'])
                ->whereIn('exam_class_id', $classIds)
                ->where('status', Exam::STATUS_PUBLISHED)
                ->whereHas('examPaper', function ($q) {
                    $q->where('create_type', ExamPaper::CREATE_TYPE_HOMEWORK);
                });

            // 获取总数
            $total = $query->count();

            // 分页查询
            $assignments = $query->orderBy('exam_end_at', 'desc')
                ->orderBy('created_at', 'desc')
                ->offset(($page - 1) * $pageSize)
                ->limit($pageSize)
                ->get();

            // 获取用户的作业完成记录
            $assignmentIds = $assignments->pluck('id')->toArray();
            $records = ExamRecord::where('user_id', $currentUser->id)
                ->whereIn('exam_id', $assignmentIds)
                ->get()
                ->keyBy('exam_id');

            // 格式化数据
            $formattedAssignments = $assignments->map(function ($assignment) use ($records) {
                $record = $records->get($assignment->id);
                $now = now();

                // 判断作业状态
                $status = 'pending'; // 待完成
                if ($record && $record->isCompleted()) {
                    $status = 'completed'; // 已完成
                } elseif ($assignment->exam_end_at && $assignment->exam_end_at < $now) {
                    $status = 'expired'; // 已过期
                } elseif ($record && $record->isInProgress()) {
                    $status = 'in_progress'; // 进行中
                }

                return [
                    'id' => $assignment->id,
                    'title' => $assignment->title,
                    'exam_class_id' => $assignment->exam_class_id,
                    'exam_class_name' => $assignment->examClass->name ?? '',
                    'exam_begin_at' => $assignment->exam_begin_at ? $assignment->exam_begin_at->format('Y-m-d H:i:s') : null,
                    'exam_end_at' => $assignment->exam_end_at ? $assignment->exam_end_at->format('Y-m-d H:i:s') : null,
                    'exam_duration' => $assignment->exam_duration,
                    'status' => $status,
                    'status_name' => $this->getStatusName($status),
                    'is_expired' => $assignment->exam_end_at && $assignment->exam_end_at < $now,
                    'is_completed' => $record && $record->isCompleted(),
                    'record' => $record ? [
                        'id' => $record->id,
                        'score' => $record->score,
                        'used_seconds' => $record->used_seconds,
                        'formatted_used_time' => $record->formatted_used_time,
                        'begin_paper_at' => $record->begin_paper_at ? $record->begin_paper_at->format('Y-m-d H:i:s') : null,
                        'end_paper_at' => $record->end_paper_at ? $record->end_paper_at->format('Y-m-d H:i:s') : null,
                        'is_passed' => $record->isPassed(),
                    ] : null,
                    'created_at' => $assignment->created_at->format('Y-m-d H:i:s'),
                ];
            });

            // 统计信息
            $allRecords = ExamRecord::where('user_id', $currentUser->id)
                ->whereHas('exam', function ($q) use ($classIds) {
                    $q->whereIn('exam_class_id', $classIds)
                      ->where('status', Exam::STATUS_PUBLISHED)
                      ->whereHas('examPaper', function ($subQ) {
                          $subQ->where('create_type', ExamPaper::CREATE_TYPE_HOMEWORK);
                      });
                })
                ->get();

            $completedCount = $allRecords->where('status', ExamRecord::STATUS_COMPLETED)->count();
            $totalAssignments = Exam::whereIn('exam_class_id', $classIds)
                ->where('status', Exam::STATUS_PUBLISHED)
                ->whereHas('examPaper', function ($q) {
                    $q->where('create_type', ExamPaper::CREATE_TYPE_HOMEWORK);
                })
                ->count();

            return [
                'code' => 200,
                'message' => '获取作业列表成功',
                'data' => [
                    'assignments' => $formattedAssignments,
                    'statistics' => [
                        'total' => $totalAssignments,
                        'completed' => $completedCount,
                        'pending' => $totalAssignments - $completedCount,
                    ],
                    'pagination' => [
                        'current_page' => $page,
                        'page_size' => $pageSize,
                        'total' => $total,
                        'total_pages' => ceil($total / $pageSize),
                        'has_more' => $page * $pageSize < $total,
                    ]
                ]
            ];

        } catch (\Exception $e) {
            return [
                'code' => 500,
                'message' => '获取作业列表失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 开始作业
     *
     * @param Request $request
     * @return array
     */
    public function start(Request $request): array
    {
        try {
            // 获取当前用户
            $currentUser = JWTAuth::parseToken()->authenticate();
            if (!$currentUser) {
                return [
                    'code' => 401,
                    'message' => '用户未登录',
                    'data' => []
                ];
            }

            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'assignment_id' => 'required|integer|min:1',
            ]);

            if ($validator->fails()) {
                return [
                    'code' => 400,
                    'message' => $validator->errors()->first(),
                    'data' => []
                ];
            }

            $assignmentId = $request->input('assignment_id');

            // 验证作业是否存在且已发布
            $assignment = Exam::with(['examClass', 'examPaper'])
                ->where('id', $assignmentId)
                ->where('status', Exam::STATUS_PUBLISHED)
                ->whereHas('examPaper', function ($q) {
                    $q->where('create_type', ExamPaper::CREATE_TYPE_HOMEWORK);
                })
                ->first();

            if (!$assignment) {
                return [
                    'code' => 404,
                    'message' => '作业不存在或未发布',
                    'data' => []
                ];
            }

            // 验证学生是否属于该班级
            $classIds = $currentUser->examClasses()->pluck('exam_class.id')->toArray();
            if (!in_array($assignment->exam_class_id, $classIds)) {
                return [
                    'code' => 403,
                    'message' => '无权限访问该作业',
                    'data' => []
                ];
            }

            // 检查作业是否过期
            if ($assignment->exam_end_at && $assignment->exam_end_at < now()) {
                return [
                    'code' => 400,
                    'message' => '作业已过期，无法开始',
                    'data' => []
                ];
            }

            // 检查是否已有记录
            $existingRecord = ExamRecord::where('user_id', $currentUser->id)
                ->where('exam_id', $assignmentId)
                ->first();

            if ($existingRecord) {
                if ($existingRecord->isCompleted()) {
                    return [
                        'code' => 400,
                        'message' => '作业已完成，无法重新开始',
                        'data' => []
                    ];
                } else {
                    // 返回现有记录
                    return [
                        'code' => 200,
                        'message' => '继续作业',
                        'data' => [
                            'record_id' => $existingRecord->id,
                            'begin_paper_at' => $existingRecord->begin_paper_at->format('Y-m-d H:i:s'),
                            'used_seconds' => $existingRecord->used_seconds,
                        ]
                    ];
                }
            }

            // 创建新的作业记录
            $record = $this->createAssignmentRecord($currentUser->id, $assignment);

            return [
                'code' => 200,
                'message' => '开始作业成功',
                'data' => [
                    'record_id' => $record['id'],
                    'begin_paper_at' => $record['begin_paper_at'],
                    'used_seconds' => 0,
                ]
            ];

        } catch (\Exception $e) {
            return [
                'code' => 500,
                'message' => '开始作业失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 获取作业试题
     *
     * @param Request $request
     * @return array
     */
    public function getQuestions(Request $request): array
    {
        try {
            // 获取当前用户
            $currentUser = JWTAuth::parseToken()->authenticate();
            if (!$currentUser) {
                return [
                    'code' => 401,
                    'message' => '用户未登录',
                    'data' => []
                ];
            }

            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'assignment_id' => 'required|integer|min:1',
            ]);

            if ($validator->fails()) {
                return [
                    'code' => 400,
                    'message' => $validator->errors()->first(),
                    'data' => []
                ];
            }

            $assignmentId = $request->input('assignment_id');

            // 验证作业记录是否存在
            $record = ExamRecord::where('user_id', $currentUser->id)
                ->where('exam_id', $assignmentId)
                ->first();

            if (!$record) {
                return [
                    'code' => 404,
                    'message' => '请先开始作业',
                    'data' => []
                ];
            }

            if ($record->isCompleted()) {
                return [
                    'code' => 400,
                    'message' => '作业已完成',
                    'data' => []
                ];
            }

            // 获取作业信息
            $assignment = Exam::with(['examPaper'])->find($assignmentId);
            if (!$assignment || !$assignment->examPaper) {
                return [
                    'code' => 404,
                    'message' => '作业不存在',
                    'data' => []
                ];
            }

            // 获取试题列表
            $questions = $assignment->examPaper->questions ?? [];

            // 获取已答题记录
            $answeredQuestions = ExamRecordQuestion::where('record_id', $record->id)
                ->get()
                ->keyBy('question_id');

            // 格式化试题数据（不包含答案）
            $formattedQuestions = [];
            foreach ($questions as $index => $questionData) {
                $questionId = $questionData['question_id'] ?? 0;
                $question = ExamQuestionPublic::find($questionId);

                if (!$question) {
                    continue;
                }

                $answeredRecord = $answeredQuestions->get($questionId);

                $formattedQuestions[] = [
                    'index' => $index + 1,
                    'question_id' => $question->id,
                    'question_sn' => $question->question_sn,
                    'title' => $question->title,
                    'type' => $question->type,
                    'type_name' => $question->type_name,
                    'question_type' => $question->question_type,
                    'question_type_name' => $question->question_type_name,
                    'post_title' => $question->post_title,
                    'model_name' => $question->model_name,
                    'more' => $question->more,
                    'score' => $questionData['score'] ?? 0,
                    'user_answer' => $answeredRecord ? $answeredRecord->user_answer : null,
                    'is_answered' => $answeredRecord !== null,
                ];
            }

            return [
                'code' => 200,
                'message' => '获取试题成功',
                'data' => [
                    'assignment' => [
                        'id' => $assignment->id,
                        'title' => $assignment->title,
                        'exam_duration' => $assignment->exam_duration,
                        'exam_end_at' => $assignment->exam_end_at ? $assignment->exam_end_at->format('Y-m-d H:i:s') : null,
                    ],
                    'record' => [
                        'id' => $record->id,
                        'begin_paper_at' => $record->begin_paper_at->format('Y-m-d H:i:s'),
                        'used_seconds' => $record->used_seconds,
                    ],
                    'questions' => $formattedQuestions,
                    'total_questions' => count($formattedQuestions),
                    'answered_count' => $answeredQuestions->count(),
                ]
            ];

        } catch (\Exception $e) {
            return [
                'code' => 500,
                'message' => '获取试题失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 提交答案
     *
     * @param Request $request
     * @return array
     */
    public function submitAnswer(Request $request): array
    {
        try {
            // 获取当前用户
            $currentUser = JWTAuth::parseToken()->authenticate();
            if (!$currentUser) {
                return [
                    'code' => 401,
                    'message' => '用户未登录',
                    'data' => []
                ];
            }

            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'assignment_id' => 'required|integer|min:1',
                'question_id' => 'required|integer|min:1',
                'answer' => 'required',
            ]);

            if ($validator->fails()) {
                return [
                    'code' => 400,
                    'message' => $validator->errors()->first(),
                    'data' => []
                ];
            }

            $assignmentId = $request->input('assignment_id');
            $questionId = $request->input('question_id');
            $userAnswer = $request->input('answer');

            // 验证作业记录是否存在
            $record = ExamRecord::where('user_id', $currentUser->id)
                ->where('exam_id', $assignmentId)
                ->first();

            if (!$record) {
                return [
                    'code' => 404,
                    'message' => '请先开始作业',
                    'data' => []
                ];
            }

            if ($record->isCompleted()) {
                return [
                    'code' => 400,
                    'message' => '作业已完成，无法修改答案',
                    'data' => []
                ];
            }

            // 检查作业是否过期
            $assignment = Exam::find($assignmentId);
            if ($assignment && $assignment->exam_end_at && $assignment->exam_end_at < now()) {
                return [
                    'code' => 400,
                    'message' => '作业已过期，无法提交答案',
                    'data' => []
                ];
            }

            // 获取试题信息
            $question = ExamQuestionPublic::find($questionId);
            if (!$question) {
                return [
                    'code' => 404,
                    'message' => '试题不存在',
                    'data' => []
                ];
            }

            // 获取试题在试卷中的配置
            $examPaper = $assignment->examPaper;
            $questionConfig = null;
            foreach ($examPaper->questions ?? [] as $q) {
                if ($q['question_id'] == $questionId) {
                    $questionConfig = $q;
                    break;
                }
            }

            if (!$questionConfig) {
                return [
                    'code' => 404,
                    'message' => '试题不在当前作业中',
                    'data' => []
                ];
            }

            // 标准化答案格式
            $userAnswer = is_array($userAnswer) ? $userAnswer : [$userAnswer];
            $correctAnswer = $question->answer_content ?
                (is_array($question->answer_content) ? $question->answer_content : [$question->answer_content]) : [];

            // 检查答案并计分
            $checkResult = ExamRecordQuestion::checkAnswer(
                $userAnswer,
                $correctAnswer,
                $question->type,
                $questionConfig['score'] ?? 0
            );

            // 保存或更新答题记录
            $questionRecord = ExamRecordQuestion::updateOrCreate(
                [
                    'record_id' => $record->id,
                    'question_id' => $questionId,
                ],
                [
                    'question_sn' => $question->question_sn,
                    'question_type' => $question->type,
                    'question_score' => $questionConfig['score'] ?? 0,
                    'user_answer' => $userAnswer,
                    'correct_answer' => $correctAnswer,
                    'is_correct' => $checkResult['is_correct'],
                    'score' => $checkResult['score'],
                ]
            );

            return [
                'code' => 200,
                'message' => '提交答案成功',
                'data' => [
                    'question_id' => $questionId,
                    'is_correct' => $checkResult['is_correct'] == 1,
                    'score' => $checkResult['score'],
                    'question_score' => $questionConfig['score'] ?? 0,
                ]
            ];

        } catch (\Exception $e) {
            return [
                'code' => 500,
                'message' => '提交答案失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 提交作业
     *
     * @param Request $request
     * @return array
     */
    public function submit(Request $request): array
    {
        try {
            // 获取当前用户
            $currentUser = JWTAuth::parseToken()->authenticate();
            if (!$currentUser) {
                return [
                    'code' => 401,
                    'message' => '用户未登录',
                    'data' => []
                ];
            }

            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'assignment_id' => 'required|integer|min:1',
                'used_seconds' => 'required|integer|min:0',
            ]);

            if ($validator->fails()) {
                return [
                    'code' => 400,
                    'message' => $validator->errors()->first(),
                    'data' => []
                ];
            }

            $assignmentId = $request->input('assignment_id');
            $usedSeconds = $request->input('used_seconds');

            // 验证作业记录是否存在
            $record = ExamRecord::where('user_id', $currentUser->id)
                ->where('exam_id', $assignmentId)
                ->first();

            if (!$record) {
                return [
                    'code' => 404,
                    'message' => '请先开始作业',
                    'data' => []
                ];
            }

            if ($record->isCompleted()) {
                return [
                    'code' => 400,
                    'message' => '作业已完成，无法重复提交',
                    'data' => []
                ];
            }

            // 检查作业是否过期
            $assignment = Exam::find($assignmentId);
            if ($assignment && $assignment->exam_end_at && $assignment->exam_end_at < now()) {
                return [
                    'code' => 400,
                    'message' => '作业已过期，无法提交',
                    'data' => []
                ];
            }

            // 计算总分
            $questionRecords = ExamRecordQuestion::where('record_id', $record->id)->get();
            $totalScore = $questionRecords->sum('score');

            // 更新作业记录
            $now = now();
            $record->update([
                'used_seconds' => $usedSeconds,
                'score' => $totalScore,
                'status' => ExamRecord::STATUS_COMPLETED,
                'end_paper_at' => $now,
            ]);

            // 获取作业信息
            $assignment = Exam::with(['examClass', 'examPaper'])->find($assignmentId);

            // 计算及格状态
            $isPassed = $record->isPassed();
            $passScore = $record->pass_score;

            return [
                'code' => 200,
                'message' => '提交作业成功',
                'data' => [
                    'assignment' => [
                        'id' => $assignment->id,
                        'title' => $assignment->title,
                        'exam_class_name' => $assignment->examClass->name ?? '',
                    ],
                    'record' => [
                        'id' => $record->id,
                        'score' => $totalScore,
                        'pass_score' => $passScore,
                        'is_passed' => $isPassed,
                        'used_seconds' => $usedSeconds,
                        'formatted_used_time' => $record->formatted_used_time,
                        'begin_paper_at' => $record->begin_paper_at->format('Y-m-d H:i:s'),
                        'end_paper_at' => $record->end_paper_at->format('Y-m-d H:i:s'),
                    ],
                    'questions' => [
                        'total' => $questionRecords->count(),
                        'correct' => $questionRecords->where('is_correct', 1)->count(),
                        'incorrect' => $questionRecords->where('is_correct', 0)->count(),
                    ]
                ]
            ];

        } catch (\Exception $e) {
            return [
                'code' => 500,
                'message' => '提交作业失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 查看作业提交状态
     *
     * @param Request $request
     * @return array
     */
    public function submissionStatus(Request $request): array
    {
        try {
            // 获取当前用户
            $currentUser = JWTAuth::parseToken()->authenticate();
            if (!$currentUser) {
                return [
                    'code' => 401,
                    'message' => '用户未登录',
                    'data' => []
                ];
            }

            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'assignment_id' => 'required|integer|min:1',
            ]);

            if ($validator->fails()) {
                return [
                    'code' => 400,
                    'message' => $validator->errors()->first(),
                    'data' => []
                ];
            }

            $assignmentId = $request->input('assignment_id');

            // 验证作业是否存在且已发布
            $assignment = Exam::with(['examClass', 'examPaper'])
                ->where('id', $assignmentId)
                ->where('status', Exam::STATUS_PUBLISHED)
                ->whereHas('examPaper', function ($q) {
                    $q->where('create_type', ExamPaper::CREATE_TYPE_HOMEWORK);
                })
                ->first();

            if (!$assignment) {
                return [
                    'code' => 404,
                    'message' => '作业不存在或未发布',
                    'data' => []
                ];
            }

            // 验证学生是否属于该班级
            $classIds = $currentUser->examClasses()->pluck('exam_class.id')->toArray();
            if (!in_array($assignment->exam_class_id, $classIds)) {
                return [
                    'code' => 403,
                    'message' => '无权限查看该作业',
                    'data' => []
                ];
            }

            // 验证作业是否在有效期内（可以查看已过期的作业状态）
            $now = now();
            $isExpired = $assignment->exam_end_at && $assignment->exam_end_at < $now;

            // 获取班级内所有学生
            $classStudents = $assignment->examClass->students()->get();
            $totalStudents = $classStudents->count();

            if ($totalStudents === 0) {
                return [
                    'code' => 200,
                    'message' => '获取作业提交状态成功',
                    'data' => [
                        'assignment_info' => [
                            'id' => $assignment->id,
                            'title' => $assignment->title,
                            'exam_class_name' => $assignment->examClass->name ?? '',
                            'exam_begin_at' => $assignment->exam_begin_at ? $assignment->exam_begin_at->format('Y-m-d H:i:s') : null,
                            'exam_end_at' => $assignment->exam_end_at ? $assignment->exam_end_at->format('Y-m-d H:i:s') : null,
                            'exam_duration' => $assignment->exam_duration,
                            'is_expired' => $isExpired,
                        ],
                        'submission_statistics' => [
                            'total_students' => 0,
                            'submitted_count' => 0,
                            'unsubmitted_count' => 0,
                            'submission_rate' => 0,
                        ],
                        'submitted_students' => [],
                    ]
                ];
            }

            // 获取该作业的所有提交记录
            $studentIds = $classStudents->pluck('id')->toArray();
            $submissionRecords = ExamRecord::with('user')
                ->where('exam_id', $assignmentId)
                ->whereIn('user_id', $studentIds)
                ->where('status', ExamRecord::STATUS_COMPLETED)
                ->orderBy('end_paper_at', 'asc')
                ->get();

            $submittedCount = $submissionRecords->count();
            $unsubmittedCount = $totalStudents - $submittedCount;
            $submissionRate = $totalStudents > 0 ? round(($submittedCount / $totalStudents) * 100, 1) : 0;

            // 格式化已提交学生列表
            $submittedStudents = $submissionRecords->map(function ($record) {
                return [
                    'student_id' => $record->user_id,
                    'student_name' => $record->user->name ?? '未知',
                    'student_number' => $record->user->number ?? '',
                    'submit_time' => $record->end_paper_at ? $record->end_paper_at->format('Y-m-d H:i:s') : null,
                    'score' => $record->score,
                    'used_seconds' => $record->used_seconds,
                    'formatted_used_time' => $record->formatted_used_time,
                    'is_passed' => $record->isPassed(),
                ];
            });

            return [
                'code' => 200,
                'message' => '获取作业提交状态成功',
                'data' => [
                    'assignment_info' => [
                        'id' => $assignment->id,
                        'title' => $assignment->title,
                        'exam_class_name' => $assignment->examClass->name ?? '',
                        'exam_begin_at' => $assignment->exam_begin_at ? $assignment->exam_begin_at->format('Y-m-d H:i:s') : null,
                        'exam_end_at' => $assignment->exam_end_at ? $assignment->exam_end_at->format('Y-m-d H:i:s') : null,
                        'exam_duration' => $assignment->exam_duration,
                        'is_expired' => $isExpired,
                    ],
                    'submission_statistics' => [
                        'total_students' => $totalStudents,
                        'submitted_count' => $submittedCount,
                        'unsubmitted_count' => $unsubmittedCount,
                        'submission_rate' => $submissionRate,
                    ],
                    'submitted_students' => $submittedStudents,
                ]
            ];

        } catch (\Exception $e) {
            return [
                'code' => 500,
                'message' => '获取作业提交状态失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 获取状态名称
     *
     * @param string $status
     * @return string
     */
    private function getStatusName(string $status): string
    {
        $statusMap = [
            'pending' => '待完成',
            'in_progress' => '进行中',
            'completed' => '已完成',
            'expired' => '已过期',
        ];

        return $statusMap[$status] ?? '未知';
    }

    /**
     * 创建作业记录
     *
     * @param int $userId
     * @param Exam $assignment
     * @return array
     */
    private function createAssignmentRecord(int $userId, Exam $assignment): array
    {
        $now = now();

        $recordId = DB::table('exam_record')->insertGetId([
            'user_id' => $userId,
            'exam_id' => $assignment->id,
            'used_seconds' => 0,
            'score' => 0,
            'status' => ExamRecord::STATUS_IN_PROGRESS, // 考试中
            'read_over_status' => ExamRecord::READ_OVER_INCOMPLETE, // 未批阅
            'begin_paper_at' => $now,
            'pass_score' => 60, // 默认及格分
            'created_at' => $now,
            'updated_at' => $now,
        ]);

        return [
            'id' => $recordId,
            'begin_paper_at' => $now->format('Y-m-d H:i:s'),
        ];
    }
}
