<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\JWTAuth\Contracts\JWTSubject;

class Admin extends Authenticatable implements JWTSubject
{
    use HasFactory, Notifiable, SoftDeletes;

    /**
     * 指定表名
     */
    protected $table = 'admin';

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'name',
        'password',
        'login_time',
    ];

    /**
     * 隐藏的属性
     */
    protected $hidden = [
        'password',
    ];

    /**
     * 属性类型转换
     */
    protected $casts = [
        'login_time' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * 获取JWT标识符
     */
    public function getJWTIdentifier()
    {
        return $this->getKey();
    }

    /**
     * 返回包含在JWT中的自定义声明
     */
    public function getJWTCustomClaims()
    {
        return [
            'guard' => 'admin',
            'type' => 'admin',
        ];
    }

    /**
     * 更新登录时间
     */
    public function updateLoginTime()
    {
        $this->update(['login_time' => now()]);
    }

    /**
     * 检查密码
     */
    public function checkPassword($password)
    {
        return password_verify($password, $this->password);
    }

    /**
     * 设置密码（自动加密）
     */
    public function setPasswordAttribute($value)
    {
        $this->attributes['password'] = password_hash($value, PASSWORD_DEFAULT);
    }
}
