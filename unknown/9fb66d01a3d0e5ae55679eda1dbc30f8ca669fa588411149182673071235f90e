<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserExamClass extends Model
{
    use HasFactory;

    /**
     * 指定表名
     */
    protected $table = 'user_exam_class';

    /**
     * 启用时间戳
     */
    public $timestamps = true;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'exam_class_id',
    ];

    /**
     * 关联到用户
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * 关联到班级
     */
    public function examClass()
    {
        return $this->belongsTo(ExamClass::class, 'exam_class_id');
    }
}
