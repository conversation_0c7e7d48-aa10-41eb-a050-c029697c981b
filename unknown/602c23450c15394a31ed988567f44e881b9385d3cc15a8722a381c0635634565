<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

class Category extends BaseModel
{
    use HasFactory, SoftDeletes;

    /**
     * 指定表名（配置文件已设置yjp_前缀）
     */
    protected $table = 'category';

    /**
     * 状态常量
     */
    const STATUS_SHOW = 1;   // 显示
    const STATUS_HIDE = 2;   // 隐藏

    /**
     * 标签常量
     */
    const TAG_MASTER = 1;    // 掌握
    const TAG_UNDERSTAND = 2; // 了解

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'parent_id',
        'name',
        'description',
        'status',
        'sort',
        'tags',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * 关联子分类
     */
    public function children()
    {
        return $this->hasMany(Category::class, 'parent_id', 'id')
            ->where('status', self::STATUS_SHOW)
            ->orderBy('sort', 'asc')
            ->orderBy('id', 'asc');
    }

    /**
     * 关联父分类
     */
    public function parent()
    {
        return $this->belongsTo(Category::class, 'parent_id', 'id');
    }

    /**
     * 递归获取所有子分类
     */
    public function allChildren()
    {
        return $this->children()->with('allChildren');
    }

    /**
     * 获取状态名称
     */
    public function getStatusNameAttribute()
    {
        return $this->status === self::STATUS_SHOW ? '显示' : '隐藏';
    }

    /**
     * 获取标签名称
     */
    public function getTagNameAttribute()
    {
        switch ($this->tags) {
            case self::TAG_MASTER:
                return '掌握';
            case self::TAG_UNDERSTAND:
                return '了解';
            default:
                return '';
        }
    }

    /**
     * 检查是否为顶级分类
     */
    public function isTopLevel()
    {
        return $this->parent_id == 0;
    }

    /**
     * 检查是否显示
     */
    public function isVisible()
    {
        return $this->status === self::STATUS_SHOW;
    }

    /**
     * 获取状态选项
     */
    public static function getStatusOptions()
    {
        return [
            self::STATUS_SHOW => '显示',
            self::STATUS_HIDE => '隐藏',
        ];
    }

    /**
     * 获取标签选项
     */
    public static function getTagOptions()
    {
        return [
            self::TAG_MASTER => '掌握',
            self::TAG_UNDERSTAND => '了解',
        ];
    }
}
