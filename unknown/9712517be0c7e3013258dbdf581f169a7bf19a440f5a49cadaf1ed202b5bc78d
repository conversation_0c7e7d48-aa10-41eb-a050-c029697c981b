<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Admin;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use <PERSON><PERSON>\JWTAuth\Facades\JWTAuth;
use <PERSON><PERSON>\JWTAuth\Exceptions\JWTException;

class AuthController extends Controller
{
    /**
     * 后台管理员登录
     *
     * @param Request $request
     * @return array
     */
    public function login(Request $request)
    {
        try {
            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'password' => 'required|string|min:6',
            ], [
                'name.required' => '用户名不能为空',
                'name.string' => '用户名必须是字符串',
                'name.max' => '用户名不能超过255个字符',
                'password.required' => '密码不能为空',
                'password.string' => '密码必须是字符串',
                'password.min' => '密码至少6个字符',
            ]);

            if ($validator->fails()) {
                return [
                    'code' => 400,
                    'message' => $validator->errors()->first(),
                    'data' => null
                ];
            }

            // 查找管理员
            $admin = Admin::where('name', $request->name)->first();

            if (!$admin) {
                return [
                    'code' => 401,
                    'message' => '用户名或密码错误',
                    'data' => null
                ];
            }
            // 验证密码
            if (!$admin->checkPassword($request->password)) {
                return [
                    'code' => 401,
                    'message' => '用户名或密码错误',
                    'data' => null
                ];
            }
            // 生成JWT token
            $token = JWTAuth::fromUser($admin);

            if (!$token) {
                return [
                    'code' => 500,
                    'message' => '登录失败，无法生成token',
                    'data' => null
                ];
            }

            // 更新登录时间
            $admin->updateLoginTime();

            return [
                'code' => 200,
                'message' => '登录成功',
                'data' => [
                    'admin' => [
                        'id' => $admin->id,
                        'name' => $admin->name,
                        'login_time' => $admin->login_time->format('Y-m-d H:i:s'),
                    ],
                    'token' => $token,
                    'token_type' => 'Bearer',
                    'expires_in' => config('jwt.ttl') * 60, // 转换为秒
                ]
            ];

        } catch (JWTException $e) {
            return [
                'code' => 500,
                'message' => '登录失败：' . $e->getMessage(),
                'data' => null
            ];
        } catch (\Exception $e) {
            return [
                'code' => 500,
                'message' => '登录失败：' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 获取当前登录的管理员信息
     *
     * @param Request $request
     * @return array
     */
    public function me(Request $request)
    {
        try {
            $admin = $request->user('admin');

            if (!$admin) {
                return [
                    'code' => 401,
                    'message' => '未登录或token已过期',
                    'data' => null
                ];
            }

            return [
                'code' => 200,
                'message' => '获取成功',
                'data' => [
                    'id' => $admin->id,
                    'name' => $admin->name,
                    'login_time' => $admin->login_time ? $admin->login_time->format('Y-m-d H:i:s') : null,
                    'created_at' => $admin->created_at->format('Y-m-d H:i:s'),
                ]
            ];

        } catch (\Exception $e) {
            return [
                'code' => 500,
                'message' => '获取用户信息失败：' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 刷新token
     *
     * @param Request $request
     * @return array
     */
    public function refresh(Request $request)
    {
        try {
            $newToken = JWTAuth::refresh();

            return [
                'code' => 200,
                'message' => 'Token刷新成功',
                'data' => [
                    'token' => $newToken,
                    'token_type' => 'Bearer',
                    'expires_in' => config('jwt.ttl') * 60,
                ]
            ];

        } catch (JWTException $e) {
            return [
                'code' => 401,
                'message' => 'Token刷新失败：' . $e->getMessage(),
                'data' => null
            ];
        } catch (\Exception $e) {
            return [
                'code' => 500,
                'message' => 'Token刷新失败：' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * 退出登录
     *
     * @param Request $request
     * @return array
     */
    public function logout(Request $request)
    {
        try {
            JWTAuth::invalidate();

            return [
                'code' => 200,
                'message' => '退出登录成功',
                'data' => null
            ];

        } catch (JWTException $e) {
            return [
                'code' => 500,
                'message' => '退出登录失败：' . $e->getMessage(),
                'data' => null
            ];
        } catch (\Exception $e) {
            return [
                'code' => 500,
                'message' => '退出登录失败：' . $e->getMessage(),
                'data' => null
            ];
        }
    }
}
