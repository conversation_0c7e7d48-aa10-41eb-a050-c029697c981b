<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CategoryQuestion extends Model
{
    use HasFactory;

    /**
     * 指定表名（配置文件已设置yjp_前缀）
     */
    protected $table = 'category_question';

    /**
     * 禁用时间戳，因为表中没有created_at和updated_at字段
     */
    public $timestamps = false;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'category_id',
        'question_id',
        'question_sn',
    ];

    /**
     * 关联分类
     */
    public function category()
    {
        return $this->belongsTo(Category::class, 'category_id', 'id');
    }

    /**
     * 关联试题
     */
    public function question()
    {
        return $this->belongsTo(ExamQuestionPublic::class, 'question_id', 'id');
    }
}
