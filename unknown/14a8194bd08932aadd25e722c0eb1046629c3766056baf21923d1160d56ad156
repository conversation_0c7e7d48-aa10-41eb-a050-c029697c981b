<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CoursewareExamClass extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * 指定表名（配置文件已设置yjp_前缀）
     */
    protected $table = 'courseware_exam_class';

    /**
     * 应用环节常量
     */
    const LINK_PREVIEW = 1; // 课前预习

    /**
     * 下载权限常量
     */
    const DOWNLOAD_ALLOWED = 1; // 允许下载
    const DOWNLOAD_FORBIDDEN = 0; // 禁止下载

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'courseware_id',
        'exam_class_id',
        'link',
        'release_time',
        'deadline',
        'start_page',
        'end_page',
        'is_download',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'release_time' => 'datetime',
        'deadline' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * 关联到课件
     */
    public function courseware()
    {
        return $this->belongsTo(Courseware::class);
    }

    /**
     * 关联到班级
     */
    public function examClass()
    {
        return $this->belongsTo(ExamClass::class);
    }

    /**
     * 获取应用环节名称
     */
    public function getLinkNameAttribute()
    {
        return $this->link === self::LINK_PREVIEW ? '课前预习' : '未知';
    }

    /**
     * 获取下载权限名称
     */
    public function getDownloadStatusAttribute()
    {
        return $this->is_download === self::DOWNLOAD_ALLOWED ? '允许下载' : '禁止下载';
    }

    /**
     * 检查是否已发布
     */
    public function isReleased()
    {
        return $this->release_time && $this->release_time <= now();
    }

    /**
     * 检查是否已过期
     */
    public function isExpired()
    {
        return $this->deadline && $this->deadline < now();
    }

    /**
     * 获取状态
     */
    public function getStatusAttribute()
    {
        if (!$this->isReleased()) {
            return '未发布';
        } elseif ($this->isExpired()) {
            return '已过期';
        } else {
            return '进行中';
        }
    }

    /**
     * 获取应用环节选项
     */
    public static function getLinkOptions()
    {
        return [
            self::LINK_PREVIEW => '课前预习',
        ];
    }

    /**
     * 获取下载权限选项
     */
    public static function getDownloadOptions()
    {
        return [
            self::DOWNLOAD_ALLOWED => '允许下载',
            self::DOWNLOAD_FORBIDDEN => '禁止下载',
        ];
    }
}
